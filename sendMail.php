<?php
// Change this to your connection info.
$conn = mysqli_connect('localhost', 'u577224695_amethical', '', '');
if (mysqli_connect_errno()) {
    // If there is an error with the connection, stop the script and display the error.
    exit('Failed to connect to MySQL: ' . mysqli_connect_error());
}

// Now we check if the data from the login form was submitted, isset() will check if the data exists.
if (isset($_POST['name'])) {
    // Could not get the data that should have been sent.
    $name = $_POST['name'];
}
if (isset($_POST['email'])) {
    // Could not get the data that should have been sent.
    $email = $_POST['email'];
}
if (isset($_POST['rfc'])) {
    // Could not get the data that should have been sent.
    $rfc = $_POST['rfc'];
}
if (isset($_POST['phone'])) {
    // Could not get the data that should have been sent.
    $phone = $_POST['phone'];
}
if (isset($_POST['name'])) {
    // Could not get the data that should have been sent.
    $msg = $_POST['msg'];
}


if ($name == null || $email == null || $rfc == null || $msg == null) {
    header('Location:index.php?info=labels#contact_form');
    exit();
}


$sql = "INSERT INTO mails(name, email, rfc, phone, msg) VALUES ('$name', '$email', '$rfc', '$phone', '$msg')";
// Prepare our SQL, preparing the SQL statement will prevent SQL injection.
if ($conn->query($sql) == TRUE) {
    header('Location:index.php?info=success#contact_form');
} else {
    header('Location:index.php?info=failed#contact_form');
}

$conn->close();