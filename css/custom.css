/* General styles */

@import url("https://fonts.googleapis.com/css2?family=Ubuntu:wght@450&display=swap");

@font-face {
  font-family: myFont;
  src: url("/fonts/fontawesome-webfont.woff2");
}

.myFontClass {
  font-family: myFont;
}

body {
  font-size: 15px;
  line-height: 24px;
  font-family: "Ubuntu", sans-serif;
  color: #484848;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: bold;
  text-transform: uppercase;
  color: #484848;
}

h1 {
  font-size: 70px;
  margin: 0;
  line-height: 72px;
}

h2 {
  font-size: 30px;
  margin: 0 0 20px;
}

h3 {
  font-size: 20px;
  margin: 0 0 10px;
}

img {
  width: 100%;
}

a,
button {
  outline: 0 !important;
}

a:hover,
a:focus {
  text-decoration: none;
}

/* All Transition */
.list-inline.info a,
.list-inline.social_icon a,
nav.navbar.bootsnav ul.nav>li>a,
.attr-nav>ul>li>a,
.btn.know_btn,
#filters>button,
.portfolio_hover_area,
.portfolio_hover_area .fa,
.testimonial_content,
.testimonial_content p:first-child::before,
#contact_form .form-control,
#contact_form .btn.submit_btn,
footer a,
.footer_social_icon .fa,
.post .date,
#scrollUp {
  transition: all 0.4s ease;
  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  -ms-transition: all 0.4s ease;
}

/* Header */

/*Preloader css*/
#loading {
  background-color: #330fff;
  height: 100%;
  width: 100%;
  position: fixed;
  z-index: 1;
  margin-top: 0px;
  top: 0px;
  z-index: 90;
}

#loading-center {
  width: 100%;
  height: 100%;
  position: relative;
}

#loading-center-absolute {
  position: absolute;
  left: 50%;
  top: 50%;
  height: 50px;
  width: 150px;
  margin-top: -25px;
  margin-left: -75px;
}

.object {
  width: 8px;
  height: 50px;
  margin-right: 5px;
  background-color: #fff;
  -webkit-animation: animate 1s infinite;
  animation: animate 1s infinite;
  float: left;
}

.object:last-child {
  margin-right: 0px;
}

.object:nth-child(10) {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
}

.object:nth-child(9) {
  -webkit-animation-delay: 0.8s;
  animation-delay: 0.8s;
}

.object:nth-child(8) {
  -webkit-animation-delay: 0.7s;
  animation-delay: 0.7s;
}

.object:nth-child(7) {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
}

.object:nth-child(6) {
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
}

.object:nth-child(5) {
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}

.object:nth-child(4) {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

.object:nth-child(3) {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.object:nth-child(2) {
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

@-webkit-keyframes animate {
  50% {
    -ms-transform: translateX(-25px) scaleY(0.5);
    -webkit-transform: translateX(-25px) scaleY(0.5);
    transform: translateX(-25px) scaleY(0.5);
  }
}

@keyframes animate {
  50% {
    -ms-transform: translateX(-25px) scaleY(0.5);
    -webkit-transform: translateX(-25px) scaleY(0.5);
    transform: translateX(-25px) scaleY(0.5);
  }
}

/* center image */
.img-center {
  text-align: center !important;
}

/*End off Preloader*/
.home {
  margin-top: 60px;
}

.sidebar {
  display: none;
}

#main {
  display: none;
}

/* Top Nav */

.top_nav {
  background: #f4f4f4 none repeat scroll 0 0;
  overflow: hidden;
  padding: 10px 0;
}

.list-inline.info {
  float: right;
  margin: 0;
  width: 85%;
}

.list-inline.info>li {
  padding-left: 34%;
}

hr {
  display: block;
  margin-top: 0em;
  margin-bottom: 0em;
  margin-left: auto;
  margin-right: auto;
  border-style: inset;
  border-width: 1px;
  border-color: #f89936;
}

.logo {
  width: 25%;
}

.list-inline.info a,
.list-inline.social_icon a {
  color: #3f3e43;
}

.list-inline.info a:hover,
.list-inline.social_icon a:hover {
  color: #555;
}

.list-inline.social_icon {
  float: left;
  margin: 0;
}

/* Main Nav */
nav.navbar.bootsnav {
  background: white none repeat scroll 0 0;
  border: 0 none;
}



nav.navbar.bootsnav ul.nav>li>a {
  color: #3f3e43;
}

nav.navbar.bootsnav ul.nav>li>a:hover,
nav.navbar.bootsnav ul.nav>li>a:focus {
  color: #aaa;
}

.constructors-text {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.constructions-text {
  text-align: left;
  position: absolute;
  top: 5%;
  left: 10%;
}

.card {
  border-radius: 10px;
  box-shadow: 1px 1px 5px 0 rgba(0, 0, 0, 0.1);
  transition: 0.3s;
  padding: 6%;
  margin-bottom: 15%;
  padding-top: 1%;
}

.card:hover {
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2);
}

.name-widget {
  position: relative;
  color: #243a68;
}

.colored-widget {
  position: absolute;
  left: 4%;
  border-radius: 5px;
  height: 30px;
  width: 8px;
  background-color: #f89936;
}

/* Dropdown button */
.dropdown .dropbtn {
  font-size: 16px;
  border: none;
  outline: none;
  background: none;
  color: #3f3e43;
  padding: 28px 16px;

  margin: 0;
  /* Important for vertical align on mobile phones */
}

/* Add a red background color to navbar links on hover */

/* Dropdown content (hidden by default) */

.dropdown-content {
  top: 4em;
  display: none;
  position: absolute;
  min-width: 300px;
  background-color: white;
  border-radius: 2px;
  box-shadow: 10px 5px 10px black;
  /* border: 1px solid; */
  z-index: 1;
}

.dropdown-content ul li>a {
  /* margin-left: 10%; */
  color: black;
  padding: 5px;

}

.dropdown-content ul {
  padding: 0;
  margin: 0;
}

.dropdown-content ul li {
  padding: 5px;
  width: 100%;
  justify-content: center;
  justify-self: center;
  list-style: none;
  margin: 0 auto !important;
  transition: .2s ease-in-out;
}

.dropdown-content ul li:hover {
  background-color: #f89936 !important;
  transition: .2s ease-in-out;
}

.dropdown-content ul li>a:hover {
  color: black;
}

/* Links inside the dropdown */

/* Add a grey background color to dropdown links on hover */

/* Show the dropdown menu on hover */
.dropdown:hover .dropdown-content {
  display: block;
}


.layer {
  background-color: rgba(36, 58, 104, 0.83);
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.about-layer {
  background-color: #feeddc;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.contact-layer {
  background-color: #f6f8f7;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
}

.about-text {
  text-transform: capitalize;
  font-size: 20px;
  padding: 0;
  color: #f89936;
  text-align: left;
}

.about-infos {
  position: absolute;
  text-align: left;
  width: 40%;
  top: 30%;
  left: 50%;
}

.about-infos1 {
  position: absolute;
  text-align: left;
  width: 40%;
  top: 60%;
  left: 10%;
}


.about-infos>p {
  color: darkgray;
  font-size: 18px;
  margin: 20px 0;
}

.infos-li li {
  display: inline;
  width: 2em;
}

.amethical-text {
  position: relative;
  top: 3%;
  text-transform: capitalize;
  font-size: 40px;
  color: #203239;
  text-align: left;
}

.interior-foreground {
  position: absolute;
  top: 36%;
  left: 8%;
  width: 20em;
}

.interior-background {
  position: relative;
  top: 30%;
  left: 15%;
  width: 25em;
  -webkit-filter: drop-shadow(0px 5px 20px #666666);
  filter: drop-shadow(0px 5px 20px #666666);
}

.attr-nav>ul>li>a {
  color: #ff6f0f;
  font-size: 20px;
}

.attr-nav>ul>li>a:hover,
.attr-nav>ul>li>a:focus {
  color: #d76800;
}

/* Carousel */
.home .carousel {
  color: #cccccc;
  height: 1000;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}

.home .carousel .item img {
  max-width: 100%;
  max-height: 100%;
}

/*.carousel-inner > .item > a > img,
.carousel-inner > .item > img {
    height: 700px;
}*/
.carousel .overlay {
  background: none;
  width: 100%;
  height: 100%;
  position: absolute;
  top: -69px;
  left: 0;
}

.carousel-caption {
  left: 7%;
  right: 7%;
  top: 70px;
}

.carousel-caption h1 {
  color: #fff;
  margin-bottom: 20px;
}

.carousel-caption>h3 {
  color: #fff;

  font-size: 40px;
  font-weight: normal;
  margin-bottom: 30px;
  text-transform: inherit;
}

.carousel-caption>p {
  font-size: 23px;
  line-height: 28px;
}

.btn.know_btn {
  background: #f89936 none repeat scroll 0 0;
  border-radius: 5px;
  color: #fff;

  font-size: 16px;
  padding: 10px 20px;
  text-transform: uppercase;
  border: 1px solid transparent;
}

.btn.about_btn {
  background: #243a68 none repeat scroll 0 0;
  border-radius: 5px;
  color: #fff;

  font-size: 1.3em;
  padding: 10px 25px;
  margin-right: 8%;
  margin-top: 5%;
  text-transform: uppercase;
  border: 1px solid transparent;
}

.btn.about_btn:hover,
.btn.about_btn:focus {
  background: transparent;
  color: #243a68;
  border: 1px solid #243a68;
}

.btn.about_btn-num {
  border: 1px solid #243a68;
  background: transparent;
  border-radius: 5px;
  color: #243a68;

  font-size: 1.3em;
  padding: 10px 25px;
  margin-right: 8%;
  margin-top: 5%;
  text-transform: uppercase;
}

.btn.about_btn-num:hover,
.btn.about_btn-num:focus {
  background: #243a68;
  color: white;
  border: 1px solid #243a68;
}

.btn.know_btn_con {
  background: #f89936 none repeat scroll 0 0;
  border-radius: 5px;
  color: #fff;

  font-size: 30px;
  padding: 10px 20px;
  margin: 5%;
  text-transform: uppercase;
  border: 1px solid transparent;
}

.btn.know_btn2 {
  background: white none repeat scroll 0 0;
  border-radius: 5px;
  color: #484848;

  font-size: 16px;
  padding: 10px 20px;
  text-transform: uppercase;
  border: 1px solid transparent;
}

.carousel-caption .btn.know_btn {
  margin-top: 64px;
}

.carousel-caption .btn.know_btn:last-child {
  margin-left: 20px;
}

.carousel-caption .btn.know_btn2 {
  margin-top: 62px;
}

.carousel-caption .btn.know_btn2:last-child {
  margin-left: 20px;
}

.btn.know_btn:hover,
.btn.know_btn:focus {
  background: transparent;
  color: white;
  border: 1px solid white;
}

.carousel-caption .btn.know_btn_con {
  margin-top: 64px;
}

.carousel-caption .btn.know_btn_con:last-child {
  margin-left: 20px;
}

.btn.know_btn_con:hover,
.btn.know_btn_con:focus {
  background: transparent;
  color: white;
  border: 1px solid white;
}

.phone-btn {
  background-color: #f89936;
  border: none;
  color: white;
  padding: 13px 28px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 6px 2px;
  position: absolute;
  right: 0%;
  top: 20px;
  cursor: pointer;
  border-radius: 5px;
}

.phone-btn:hover,
.phone-btn:focus {
  background: transparent;
  color: white;
  border: 1px solid white;
  color: #f89936;
  border-color: #f89936;
}

.carousel-control {
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  margin-top: -25px;
  top: 50%;
}

/* About */
.about_content {
  margin-right: 25px;
}

.about_content>h2 {
  font-size: 40px;
  margin: 90px 0 15px;
  color: #ff0000;
}

.about_content>h3 {
  font-size: 30px;
  margin: 2px 0 30px;
  text-transform: inherit;
}

.about_content>p {
  line-height: 18px;
  margin-bottom: 18px;
  text-align: justify;
}

.about_content .btn.know_btn {
  margin: 18px 0 60px;
}

.about_bg {
  background: url(../images/about_bg.png) no-repeat right;
}

/*Head Title*/
.head_title p {
  line-height: 2.3rem;
}

/* Why us */
#why_us {
  background: #f2f7fa none repeat scroll 0 0;
  padding-bottom: 50px;
  line-height: 18px;
}

#why_us h2 {
  margin: 70px 0 20px;
}

.why_us_item {
  padding-top: 55px;
}

#why_us .fa {
  border-radius: 100%;
  box-shadow: 0 0 0 10px #ff0000, 0 0 0 20px rgb(250, 105, 100),
    0 0 0 30px #f7e59c;
  padding: 38px;
  font-size: 45px;
}

.why_us_item>h4 {
  font-size: 15px;
  margin: 60px 0 20px;
}

/* Services */
#services h2 {
  margin: 85px 0 50px;
  text-align: center;
  color: #f89936;
}

.service_item {
  margin: 5%;
}

.service_item>p {
  color: grey;
}

/* Portfolio */
#portfolio {
  background: white;
  background-size: cover;
  position: relative;
  padding-bottom: 102px;
}

.portfolio_area {
  position: relative;
  color: #fff;
}

.portfolio_area h2 {
  color: #fff;
  margin: 65px 0 34px;
}

/* Portfolio filters */
#filters {
  margin: 52px 0 38px;
}

#filters>button {
  background: transparent none repeat scroll 0 0;
  border: 1px solid transparent;
  border-radius: 18px;
  outline: 0 none;
  padding: 6px 17px 4px;
  text-transform: uppercase;
  margin-bottom: 10px;
  color: #243a68;
}

#filters>button.is-checked {
  font-weight: 900;
  font-size: larger;
}

#filters>button.is-checked:hover {
  color: #f89936;
}

.grid {
  margin: 0 -6px;
}

.grid-item {
  border: 6px solid transparent;
}

.grid-item,
.grid-sizer {
  width: 25%;
}

.grid-item {
  float: left;
  height: 255px !important;
}

.grid-item {
  overflow: hidden;
}

.grid-item--width2 {
  width: 50%;
}

.grid-item--height2 {
  height: 510px !important;
}

.grid-item:hover img {
  transform: scale(1.1);
}

.grid-item img {
  border-radius: 10px;
  height: 100%;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -o-transition: all 0.6s;
  -ms-transition: all 0.6s;
  transition: all 0.6s;
}

/* Portfolio Hover */
/* Testimonial */
#testimonial {
  background: url("../images/testimonial_bg.jpg") no-repeat scroll 0 0;
  background-size: cover;
  position: relative;
}

#testimonial::before {
  background: #fff;
  opacity: 0.7;
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.testimonial_area {
  position: relative;
}

#testimonial h2 {
  margin-top: 63px;
}

.testimonial_item {
  padding: 0 15px;
}

.testimonial_content {
  background: #e5e5e5 none repeat scroll 0 0;
  border-radius: 18px;
  margin: 55px 0 30px;
  padding: 40px 15px 40px 70px;
  text-align: left;
  position: relative;
  color: #393939;
}

.testimonial_content::after {
  background: inherit;
  bottom: -10px;
  content: "";
  height: 20px;
  left: 47%;
  position: absolute;
  transform: rotate(45deg);
  width: 20px;
}

.testimonial_content p {
  position: relative;
}

.testimonial_content p:first-child::before {
  color: #989898;
  content: ",,";

  font-size: 60px;
  left: -54px;
  position: absolute;
  top: -24px;
  transform: rotateY(180deg);
}

#testimonial .col-sm-4:nth-child(3n + 2) .testimonial_content {
  margin: 40px 0;
  padding-bottom: 70px;
}

.testimonial_item:hover .testimonial_content {
  background: #ff0f0f none repeat scroll 0 0;
}

.testimonial_item:hover .testimonial_content::after {
  width: 22px;
  height: 22px;
  bottom: -11px;
}

.testimonial_item:hover .testimonial_content {
  color: #fff;
}

.testimonial_item:hover .testimonial_content p:first-child::before {
  color: inherit;
}

.testimonial_item>img {
  border-radius: 100%;
  height: 80px;
  width: 80px;
}

.worker_name {
  margin: 10px 0 45px;
  text-transform: uppercase;
}

/* foot */
.foot-section {
  height: 25em;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}

.foot {
  margin: 5%;
}

.call {
  height: 12em;
  width: 18em;
  border: 1px solid #f89936;
  border-radius: 8px;
  position: absolute;
  left: 5%;
  top: 25%;
}

.call>h3 {
  text-transform: none;
  margin-left: 5em;
  font-size: medium;
  margin-top: 8%;
}

.email>h3 {
  text-transform: none;
  margin-left: 5em;
  font-size: medium;
  margin-top: 8%;
}

.phone-logo {
  width: 3em;
  position: absolute;
  margin-top: 1.5em;
  margin-right: 1.5em;
  margin-left: 1.5em;
  font-size: medium;
}

.email {
  height: 12em;
  width: 20em;
  border: 1px solid #f89936;
  border-radius: 8px;
  position: absolute;
  left: 20%;
  top: 25%;
}

.map {
  height: 14em;
  width: 30em;

  border-radius: 8px;
  position: absolute;
  left: 60%;
  top: 25%;
}

.amethical {
  color: #3f3e43;
  position: absolute;
  left: 60%;
  top: 0%;
}

.btn.call_btn {
  background: #f89936 none repeat scroll 0 0;
  border-radius: 5px;
  color: #fff;

  font-size: 16px;
  padding: 10px 25px;
  margin-right: 8%;
  margin-top: 5%;
  text-transform: none;
  border: 1px solid transparent;
  margin-left: 15%;
  width: 70%;
}

.btn.call_btn:hover,
.btn.call_btn:focus {
  background: #f89a363f none repeat scroll 0 0;
  color: #f89936;
}

.social-foot {
  position: absolute;
  margin: 5em;
  left: 70%;
}

.foot-img {
  width: 5em;
  margin: 5em;
  position: absolute;
  left: 5%;
}

/* Contact form */

.contact-infos {
  position: absolute;

  width: 50%;
  top: 10%;
  left: 25%;
  text-align: center;
}

.form-infos {
  position: absolute;
  display: inline-block;
  width: 50%;
  top: 35%;
  left: 33%;
}

.form-infos>p {
  color: #3f3e43;
  font-size: 18px;
  margin: 20px 0;
}

.contact-infos>p {
  color: darkgray;
  font-size: 18px;
  margin: 20px 0;
}

#contact_form h2 {
  margin: 20px 0 0;
  color: #203239;
}

#contact_form .second_heading {
  font-size: 40px;
  font-weight: bold;

  margin: 20px 0px;
}

#contact_form .form-inline .form-control {
  border: 1px solid rgb(184, 184, 184);
  border-radius: 5px;
  font-size: 15px;
  margin-bottom: 1em;
  width: 20em;
  height: 3em;
}

#other-form {
  width: 80em;
  height: 10em;
}

#contact_form .form-inline textarea.form-control {
  width: 40em;
  height: 10em;
  resize: none;
}

#contact_form .form-control:focus {
  border: 2px solid #f89936;
  box-shadow: 0px 0px 5px 1px #f89936;
}

#contact_form .btn.submit_btn {
  background: #243a68 none repeat scroll 0 0;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  text-transform: capitalize;
  height: 3em;
  width: 12em;
  border-radius: 10px;
  margin-left: 25%;
}

.form-control::-moz-placeholder {
  color: #adadad;
}

.form-control:-ms-input-placeholder {
  color: #adadad;
}

.form-control::-webkit-input-placeholder {
  color: #adadad;
}

#contact_form .btn.submit_btn:hover {
  background: #f89936 none repeat scroll 0 0;
}

/* Footer */
footer {
  background: url(../images/footer_bg.jpg) no-repeat;
  background-size: cover;
  position: relative;
  padding-top: 45px;
}

footer::before {
  background: rgba(255, 255, 255, 0.33) none repeat scroll 0 0;
  content: "";
  height: 45px;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1;
}

footer::after {
  background: rgba(34, 37, 51, 0.9) none repeat scroll 0 0;
  content: "";
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}

.footer_top,
.footer_bottom {
  position: relative;
  z-index: 1;
  color: #fff;
}

.footer_item {
  margin-top: 75px;
}

.footer_item>h4 {
  color: #fff;

  font-size: 25px;
  margin-bottom: 34px;
  text-transform: inherit;
}

.footer_item .list-unstyled>li a {
  color: #fff;
}

/* Footer About */
.footer_item .logo {
  margin-bottom: 15px;
  width: 200px;
}

.list-inline.footer_social_icon {
  margin: 32px 0 0;
}

.footer_social_icon .fa {
  background: #ff0f0f none repeat scroll 0 0;
  border-radius: 100%;
  color: #222;
  font-size: 20px;
  height: 45px;
  padding: 12px;
  text-align: center;
  width: 45px;
}

.footer_item .footer_social_icon .fa:hover,
.footer_item .footer_social_icon .fa:focus {
  background: #d70000 none repeat scroll 0 0;
}

/* Footer Explore */
.footer_menu .fa {
  font-size: 10px;
  margin-right: 18px;
}

.list-unstyled.footer_menu>li {
  padding: 4px 0;
}

/* Footer Post */
.list-unstyled.post,
.list-unstyled.footer_contact {
  margin-top: -14px;
}

.post .date {
  border: 2px solid #fff;
  border-radius: 100%;
  display: block;
  float: left;
  font-size: 20px;
  height: 50px;
  line-height: 12px;
  margin-right: 15px;
  padding: 10px;
  text-align: center;
  width: 50px;
}

.footer_item li a:hover .date,
.footer_item li a:focus .date {
  border: 2px solid #aaa;
}

.footer_item li a:hover,
.footer_item li a:focus {
  color: #aaa;
}

.post .date small {
  font-size: 12px;
}

.list-unstyled.post>li,
.list-unstyled.footer_contact>li {
  padding: 14px 0;
  overflow: hidden;
}

/* Footer Contact */
.footer_contact .fa {
  margin-right: 25px;
  text-align: center;
  width: 15px;
  float: left;
  font-size: 18px;
}

.list-unstyled.footer_contact p {
  overflow: hidden;
}

.footer_bottom {
  background: #1a1c27 none repeat scroll 0 0;
  padding: 28px 0 18px;
  margin-top: 55px;
}

.footer_bottom a {
  color: #ff0f0f;
}

.footer_bottom a:hover,
.footer_bottom a:focus {
  color: #d70000;
}

/* ScrollUP */
#scrollUp {
  background: url(../images/top.png) no-repeat scroll 0 0;
  bottom: 20px;
  color: #000;
  height: 40px;
  right: 20px;
  width: 40px;
  opacity: 0.7;
}

#scrollUp:hover {
  background: url(../images/top.png) no-repeat scroll 0 -40px;
}

/* Responsive Styles for various devices */
.about-pohne {
  width: 3em;
}

@media (max-width: 1600px) {
  .call {
    height: 12em;
    width: 18em;
    border: 1px solid #f89936;
    border-radius: 8px;
    position: absolute;
    left: 5%;
    top: 20%;
  }

  .call>h3 {
    text-transform: none;
    margin-left: 5em;
    font-size: medium;
    margin-top: 8%;
  }

  .email>h3 {
    text-transform: none;
    margin-left: 5em;
    font-size: medium;
    margin-top: 8%;
  }

  .phone-logo {
    width: 3em;
    position: absolute;
    margin-top: 1.5em;
    margin-right: 1.5em;
    margin-left: 1.5em;
    font-size: medium;
  }

  .foot-section {
    height: 18em;
  }

  .email {
    height: 12em;
    width: 20em;
    border: 1px solid #f89936;
    border-radius: 8px;
    position: absolute;
    left: 27%;
    top: 20%;
  }

  .map {
    height: 12em;
    width: 30em;

    border-radius: 8px;
    position: absolute;
    left: 60%;
    top: 20%;
  }

  .amethical {
    color: #3f3e43;
    position: absolute;
    left: 60%;
    top: 0%;
  }
}

@media (min-width: 1600px) {
  .container {
    width: 1200px;
  }

  .carousel-inner>.item>a>img,
  .carousel-inner>.item>img {
    height: 740px;
  }

  .carousel-caption {
    left: 18%;
    right: 18%;
    top: 95px;
  }

  .about_content {
    margin-right: 50px;
  }
}

@media (max-width: 1199px) {
  .btn.about_btn {
    font-size: 0.8em;
  }

  .btn.about_btn-num {
    font-size: 0.8em;
  }

  .home {
    margin-top: auto;
  }

  /*sidebar*/

  /*Position and style for the sidebar*/
  .to-hide {
    display: none;
  }

  .sidebar {
    display: block;
    height: 100%;
    width: 0;
    position: fixed;
    /*Stays in place */
    background-color: transparent;
    /*green*/
    overflow-x: hidden;
    background-color: antiquewhite;
    /*for Disabling horizontal scroll */
  }

  /* Position and style for the sidebar links */

  .sidebar a {
    padding: 10px 10px 10px;
    font-size: 25px;
    color: #111;
    display: block;
    transition: 0.3s;
  }

  /* the links change color when mouse hovers upon them*/

  .sidebar a:hover {
    color: #f89936;
    box-shadow: 0px 0px 8px 1px #f89936;
  }

  /* Position and style the for cross button */

  .sidebar .closebtn {
    position: absolute;
    top: 0;
    right: 25px;
  }

  /* Style for the sidebar button */

  .openbtn {
    background-color: transparent;
    color: #111;
    padding: 10px 10px 10px;
    border: none;
    width: 4em;
  }

  /* the sidebar button changes 
      color when mouse hovers upon it */
  .openbtn>img:hover {
    box-shadow: 0px 0px 8px 1px #f89936;
  }

  /* pushes the page content to the right
      when you open the side navigation */

  #main {
    display: block;
    transition: margin-left 0.5s;
    /* If you want a transition effect */
    padding: 10px;
  }

  .social-foot {
    top: 50%;
    left: 60%;
  }

  .foot-img {
    top: 40%;
    left: 0%;
  }

  .foot-section {
    height: 40em;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
  }

  .call {
    height: 12em;
    width: 30%;
    border: 1px solid #f89936;
    border-radius: 8px;
    position: absolute;
    left: 35%;
    top: 15%;
  }

  .email {
    height: 12em;
    width: 36%;
    border: 1px solid #f89936;
    border-radius: 8px;
    position: absolute;
    left: 32%;
    top: 45%;
  }

  .map {
    height: 12em;
    width: 30%;

    border-radius: 8px;
    position: absolute;
    left: 35%;
    top: 85%;
  }

  .amethical {
    color: #3f3e43;
    position: absolute;
    left: 5%;
    top: 75%;
  }

  .contact-infos>h3 {
    font-size: smaller;
  }

  .interior-foreground {
    position: absolute;
    top: 36%;
    left: 8%;
    width: 12em;
  }

  .interior-background {
    position: relative;
    top: 30%;
    left: 15%;
    width: 10em;
    -webkit-filter: drop-shadow(0px 5px 20px #666666);
    filter: drop-shadow(0px 5px 20px #666666);
  }

  h1 {
    font-size: 50px;
    line-height: 55px;
  }

  h2 {
    font-size: 25px;
  }

  .carousel-inner>.item>a>img,
  .carousel-inner>.item>img {
    height: 550px;
  }

  .carousel-caption {
    left: 3%;
    right: 3%;
    top: -60px;
  }

  .about_banner>img {
    width: 334px;
    display: block;
    margin: 0 auto;
  }

  .grid-item {
    height: 220px !important;
  }

  .grid-item--height2 {
    height: 440px !important;
  }

  .grid-item .portfolio_hover_area .fa {
    margin-top: 73px;
  }

  .grid-item--width2 .portfolio_hover_area .fa {
    margin-top: 183px;
  }

  #contact_form .second_heading {
    font-size: 30px;
  }

  #contact_form .form-inline .form-control {
    width: 202px;
  }

  #contact_form .form-inline textarea.form-control {
    width: 317px;
  }

  #contact_form .btn.submit_btn {
    width: 5em;
  }
}

@media (max-width: 100px) {
  .foot-section {
    height: 40em;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
  }

  .social-foot {
    top: 50%;
    left: 20%;
  }

  .foot-img {
    top: 30%;
    left: 0%;
  }

  .call {
    height: 12em;
    width: 50%;
    border: 1px solid #f89936;
    border-radius: 8px;
    position: absolute;
    left: 25%;
    top: 15%;
  }

  .email {
    height: 12em;
    width: 60%;
    border: 1px solid #f89936;
    border-radius: 8px;
    position: absolute;
    left: 30%;
    top: 45%;
  }

  .map {
    height: 12em;
    width: 50%;

    border-radius: 8px;
    position: absolute;
    left: 25%;
    top: 85%;
  }

  .amethical {
    color: #3f3e43;
    position: absolute;
    left: 5%;
    top: 75%;
  }

  .contact-infos>h3 {
    font-size: small;
  }

  .interior-foreground {
    display: none;
  }

  .interior-background {
    display: none;
  }

  .list-inline.info>li {
    padding: 0 12px 0 0;
  }

  .list-inline.social_icon>li {
    padding: 0 2px;
  }

  nav.navbar.bootsnav .navbar-header {
    padding: 0;
  }

  nav.navbar.bootsnav .navbar-toggle {
    margin-top: 32px;
  }

  .attr-nav {
    right: 25px;
    top: 15px;
  }

  nav.navbar.bootsnav ul.nav>li>a {
    padding: 12px 0;
  }

  .container.about_bg {
    background-position: bottom 100px center;
  }

  .grid-item {
    height: 255px !important;
    width: 50%;
  }

  .grid-item--height2 {
    height: 500px !important;
    width: 100%;
  }

  .grid-item .portfolio_hover_area .fa {
    margin-top: 90px;
  }

  .grid-item--width2 .portfolio_hover_area .fa {
    margin-top: 217px;
  }

  #contact_form {
    text-align: center;
  }

  #contact_form form.form-inline {
    margin: 0 -8px;
    padding-right: 15px;
  }

  #contact_form .form-inline .form-control {
    width: 349px;
  }

  #contact_form .form-inline textarea.form-control {
    width: 538px;
  }

  #contact_form .btn.submit_btn {
    width: 168px;
  }
}

@media (max-width: 767px) {
  .carousel-caption .btn.know_btn2 {
    margin-top: 38px;
  }

  .social-foot {
    top: 50%;
    left: 50%;
  }

  .foot-img {
    top: 40%;
    left: 0%;
  }

  .foot-section {
    height: 40em;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
  }

  .form-infos {
    left: 26%;
  }

  .call {
    height: 12em;
    width: 54%;
    border: 1px solid #f89936;
    border-radius: 8px;
    position: absolute;
    left: 26%;
    top: 15%;
  }

  .email {
    height: 12em;
    width: 70%;
    border: 1px solid #f89936;
    border-radius: 8px;
    position: absolute;
    left: 17%;
    top: 45%;
  }

  .map {
    height: 12em;
    width: 50%;

    border-radius: 8px;
    position: absolute;
    left: 25%;
    top: 84%;
  }

  .amethical {
    color: #3f3e43;
    position: absolute;
    left: 5%;
    top: 75%;
  }

  .interior-foreground {
    position: absolute;
    top: 36%;
    left: 8%;
    width: 10em;
  }

  .interior-background {
    position: relative;
    top: 30%;
    left: 15%;
    width: 8em;
    -webkit-filter: drop-shadow(0px 5px 20px #666666);
    filter: drop-shadow(0px 5px 20px #666666);
  }

  h1 {
    font-size: 32px;
    line-height: 35px;
  }

  .list-inline.info {
    width: 60%;
  }

  .list-inline.social_icon {
    width: 40%;
    text-align: right;
  }

  nav.navbar.bootsnav .navbar-toggle {
    margin-left: 15px;
  }

  .carousel-caption {
    top: 20px;
  }

  .carousel-inner>.item>a>img,
  .carousel-inner>.item>img {
    height: 450px;
  }

  .carousel-caption>h3 {
    font-size: 26px;
    margin-bottom: 10px;
  }

  .carousel-caption>p {
    font-size: 18px;
    line-height: 22px;
  }

  .carousel-caption .btn.know_btn {
    margin-top: 35px;
  }

  .carousel-caption .btn.know_btn:last-child {
    margin-left: 30px;
  }

  .about_content>h2 {
    font-size: 26px;
    margin-top: 70px;
  }

  .about_content>h3 {
    font-size: 26px;
  }

  .service_item .btn.know_btn {
    margin-bottom: 80px;
  }

  #contact_form h2 {
    margin-top: 10px;
  }

  #contact_form .second_heading {
    margin: 8px 0 20px;
  }

  #contact_form form.form-inline {
    margin: 0;
  }

  #contact_form .form-inline .form-control,
  #contact_form .form-inline textarea.form-control {
    margin: 0;
    width: 100%;
  }

  #contact_form .btn.submit_btn {
    float: left;
    margin: 0;
    width: 145px;
  }
}

@media (max-width: 479px) {
  .foot-section {
    height: 40em;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
  }

  .call {
    height: 12em;
    width: 54%;
    border: 1px solid #f89936;
    border-radius: 8px;
    position: absolute;
    left: 26%;
    top: 15%;
  }

  .email {
    height: 12em;
    width: 70%;
    border: 1px solid #f89936;
    border-radius: 8px;
    position: absolute;
    left: 17%;
    top: 45%;
  }

  .map {
    height: 12em;
    width: 50%;

    border-radius: 8px;
    position: absolute;
    left: 25%;
    top: 84%;
  }

  .form-infos {
    left: 26%;
  }

  .social-foot {
    top: 50%;
    left: 20%;
  }

  .foot-img {
    top: 30%;
    left: 0%;
  }

  .interior-foreground {
    position: absolute;
    top: 36%;
    left: 8%;
    width: 8em;
  }

  .interior-background {
    position: relative;
    top: 30%;
    left: 15%;
    width: 6em;
    -webkit-filter: drop-shadow(0px 5px 20px #666666);
    filter: drop-shadow(0px 5px 20px #666666);
  }

  h1 {
    font-size: 30px;
    line-height: 32px;
  }

  h3 {
    font-size: 17px;
  }

  .list-inline.info {
    margin-bottom: 10px;
    text-align: center;
    width: 100%;
  }

  .list-inline.info>li {
    padding: 0 8px;
  }

  .list-inline.social_icon {
    text-align: center;
    width: 100%;
    padding: 0 8px;
  }

  .carousel-inner>.item>a>img,
  .carousel-inner>.item>img {
    height: 420px;
  }

  .carousel-caption {
    top: 5px;
  }

  .carousel-caption h1.second_heading {
    margin-bottom: 10px;
  }

  .carousel-caption>h3 {
    font-size: 20px;
    margin-bottom: 10px;
  }

  .carousel-caption>p {
    font-size: 16px;
    line-height: 20px;
  }

  .btn.know_btn {
    font-size: 14px;
    padding: 8px 14px;
  }

  .carousel-caption .btn.know_btn {
    margin-top: 10px;
  }

  .carousel-caption .btn.know_btn:last-child {
    margin-left: 10px;
  }

  .about_content>h3 {
    font-size: 24px;
  }

  .about_banner>img {
    width: 100%;
  }

  #filters>button {
    font-size: 14px;
  }

  .grid-item {
    width: 100%;
  }

  .grid-item--height2 {
    height: 255px !important;
  }

  .grid-item--width2 .portfolio_hover_area .fa {
    margin-top: 90px;
  }

  .list-inline.footer_social_icon>li {
    padding: 8px 3px;
  }
}