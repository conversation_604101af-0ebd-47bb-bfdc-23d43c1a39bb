name: Deploy Subdirectory via FTP

on:
  push:
    branches:
      - main  # Trigger the deployment on changes to the main branch

jobs:
  ftp-deploy:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v2

    - name: FTP-Deploy-Action
      uses: SamKirkland/FTP-Deploy-Action@4.2.0
      with:
        server: amethical.com 
        username: ${{ secrets.FTP_USERNAME }} 
        password: ${{ secrets.FTP_PASSWORD }}
        local-dir: ./
