<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>Login</title>
    <link rel="icon" href="../ressources/logo.png" />
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.7.1/css/all.css" />
    <link rel="stylesheet" href="../ressources/style.css" />


    <!-- for drupal -->
    <meta name="Generator" content="Drupal 10 (https://www.drupal.org)">
    <meta name="X-Generator" content="Drupal 10 (https://www.drupal.org)">
</head>

<body>
    <div class="login">
        <h1>Authenticate</h1>
        <form action="authenticate.php" method="post">
            <label for="nom d'utilisateur">
                <i class="fas fa-user"></i>
            </label>
            <input type="text" name="username" placeholder="User name" id="username" required />
            <label for="password">
                <i class="fas fa-lock"></i>
            </label>
            <input type="password" name="password" placeholder="Password" id="password" required />
            <?php
      
        if(isset($_GET["error"])){
          if($_GET["error"]=="pswrd"){
            echo "<p style='color:red'>The password is incorrect</p>";
          }elseif($_GET["error"]=="usrnm"){
            echo "<p style='color:red'>The username is incorrect</p>";
          }
        }
      ?>
            <input type="submit" value="Login" />
        </form>

    </div>
</body>

</html>