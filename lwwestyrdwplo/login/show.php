<?php
// We need to use sessions, so you should always start sessions using the below code.
session_start();
// If the user is not logged in redirect to the login page...
if (!isset($_SESSION['loggedin'])) {
    header('Location: index.html');
    exit;
}
?>

<?php
// connect to database
$conn = mysqli_connect('localhost', 'u577224695_amethical', 'FresherCode001_', 'u577224695_phplogin');
if (isset($_GET['go'])) {
    $reff = addslashes($_GET['reff_searched']);
    $sql = "SELECT * FROM mails where id='$reff'";
    $title = 'the result of your search';
} else {
    $sql = "SELECT * FROM mails ORDER BY id DESC LIMIT 0, 10";
    $title = "show the 10 latest emails";
}

$result = mysqli_query($conn, $sql);

$files = mysqli_fetch_all($result, MYSQLI_ASSOC);

?>



<!DOCTYPE html>
<html>

<head>

    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700,800,900" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="../css/style.css">



    <link rel="icon" href="../ressources/logo.png" />
    <meta charset="utf-8">
    <title>Show all mails</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.7.1/css/all.css">
</head>

<body class="loggedin">






    <div class="wrapper d-flex align-items-stretch">
        <nav id="sidebar">
            <div class="custom-menu">
                <button type="button" id="sidebarCollapse" class="btn btn-primary">
                    <i class="fa fa-bars"></i>
                    <span class="sr-only">Toggle Menu</span>
                </button>
            </div>
            <ul class="list-unstyled components mb-5">

                <li class="active">
                    <a href="home.php"><span class="fa fa-home mr-3"></span> Home</a>
                </li>

                <li>
                    <a href="./show.php"><span class="fa fa-file mr-3"></span> Mails</a>
                </li>

                <li>
                    <a href="#"><span class="fa fa-users-cog mr-3"></span> Contacts</a>
                </li>


                <li>
                    <a href="logout.php"><span class="fa fa-sign-out-alt mr-3"></span> Logout</a>
                </li>


            </ul>

        </nav>










        <div style="width: 100%; margin:8%">
            <div class="content">
                <h2><?php echo $title ?></h2>
                <div class="container">

                    <?php foreach ($files as $file) : ?>

                        <div style="margin: 2em" class="card">
                            <div class="card-header">
                                phone: <?php echo $file['phone']; ?>
                            </div>
                            <div class="card-body">
                                <h5 class="card-title" style="color: green;">name</h5>
                                <p class="card-text"><?php echo $file['name']; ?></p>
                                <h5 class="card-title" style="color: green;">email</h5>
                                <p class="card-text"><?php echo $file['email']; ?></p>
                                <h5 class="card-title" style="color: green;">the reason for this contacting</h5>
                                <p class="card-text"><?php echo $file['rfc']; ?></p>
                                <h5 class="card-title" style="color: green;">the message</h5>
                                <p class="card-text"><?php echo $file['msg']; ?></p>
                            </div>
                        </div>

                    <?php endforeach; ?>
                </div>
            </div>

        </div>

        <script src="../js/jquery.min.js"></script>
        <script src="../js/popper.js"></script>
        <script src="../js/bootstrap.min.js"></script>
        <script src="../js/main.js"></script>

    </div>











</body>

</html>