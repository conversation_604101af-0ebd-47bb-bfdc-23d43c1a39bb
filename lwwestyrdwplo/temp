<table class="styled-table">
            <thead>
                <th>ID</th>
                <th>refference</th>
                <th>document</th>
                <th>commentaire</th>
                <th>taille (en kb)</th>
                <th>Téléchargement</th>
            </thead>
            <tbody>
                <?php foreach ($files as $file) : ?>
                    <tr>
                        <td><?php echo $file['id']; ?></td>
                        <td><?php echo $file['refference']; ?></td>
                        <td><?php echo $file['fileN']; ?></td>
                        <td><?php echo $file['comment']; ?></td>
                        <td><?php echo floor($file['size'] / 1000) . ' KB'; ?></td>
                        <td><a href="show.php?file_id=<?php echo $file['id'] ?>">Télécharger</a></td>
                    </tr>

                <?php endforeach; ?>

            </tbody>
        </table>